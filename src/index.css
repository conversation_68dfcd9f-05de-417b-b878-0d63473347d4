@import "./data/styles.css";

:root {
    font-family: var(--primary-font);
    line-height: var(--body-line-height);
    font-weight: var(--body-weight);

    color-scheme: light dark;
    /* background-color removed to prevent conflicts with App.css backgrounds */

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    max-height: 100%;
    max-width: 100%;
}

body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
}

a {
    font-weight: 500;
    color: var(--link-color);
    text-decoration: none;
    transition: color var(--transition-fast), transform var(--transition-fast);
    position: relative;
}

a:hover {
    color: var(--accent-color);
    transform: translateY(-1px);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--secondary-font);
    font-weight: var(--heading-weight);
    line-height: var(--heading-line-height);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.02em;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
    padding: 0;
    margin: 0;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: #1a1a1a;
    cursor: pointer;
    transition: border-color 0.25s;
}

button:hover {
    border-color: #727272;
}

button:focus,
button:focus-visible {
    outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #1c1c1e; /* iOS primary label color */
        /* background-color removed to prevent conflicts with App.css backgrounds */
    }

    a:hover {
        color: var(--accent-color);
    }

    button {
        background-color: #f2f2f7; /* iOS system background */
    }
}