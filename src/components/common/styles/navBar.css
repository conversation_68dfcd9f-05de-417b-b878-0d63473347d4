@import "../../../data/styles.css";

.nav-container {
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    padding: 20px 0;
    pointer-events: none; /* Allow clicks to pass through container */
}

.navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.nav-background {
    display: flex;
    width: 100%;
    height: 50px;
    background: var(--glass-base);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 25px;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 0 25px; /* Reduced right padding to give dark mode switch more space */
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
    pointer-events: auto; /* Re-enable clicks on navbar */
}

.nav-list {
    display: flex;
    justify-content: space-around;
    list-style: none;
    align-items: center;
    margin: 0;
    padding: 0;
    flex: 1;
    gap: 10px;
}

.divider {
    height: 30px;
    margin: 0 8px 0 12px; /* Asymmetric margins: more space on left, less on right */
    opacity: 0.3;
}

.nav-item {
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background-color: var(--hover-overlay);
    transform: translateY(-2px);
}

.nav-item.active {
    background-color: var(--active-overlay);
    font-weight: 700;
}

.nav-item a {
    text-decoration: none;
    color: var(--primary-color);
    display: block;
    width: 100%;
    height: 100%;
    transition: color var(--transition-fast);
}

.nav-item:hover a {
    color: var(--icon-hover);
}

.bg-blur {
    background: rgba(197, 197, 197, 0.25);
    backdrop-filter: blur(15px);
}

/* Dark mode styles */
.dark .nav-background {
    background: var(--dark-glass-base);
    border: 1px solid var(--dark-glass-border);
}

.dark .nav-item:hover {
    background-color: var(--dark-hover-overlay);
}

.dark .nav-item.active {
    background-color: var(--dark-active-overlay);
}

.dark .nav-item a {
    color: var(--dark-secondary-text);
}

.dark .nav-item:hover a {
    color: var(--dark-icon-hover);
}

.dark .nav-item.active a {
    color: var(--dark-primary-text);
}

/* Enhanced dark mode navbar styling */
.dark .nav-background:hover {
    background: var(--dark-glass-hover);
    border-color: var(--dark-glass-border-hover);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-container {
        padding: 15px 0;
    }

    .navbar {
        max-width: 400px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 12px 0;
    }

    .navbar {
        max-width: 350px;
    }

    .nav-background {
        height: 45px;
        padding: 0 20px;
    }

    .nav-item {
        font-size: 0.9rem;
        padding: 6px 12px;
    }

    .divider {
        height: 25px;
        margin: 0 8px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 10px 0;
    }

    .navbar {
        max-width: 300px;
    }

    .nav-background {
        height: 40px;
        padding: 0 15px;
    }

    .nav-item {
        font-size: 0.85rem;
        padding: 5px 10px;
    }

    .nav-list {
        gap: 5px;
    }

    .divider {
        height: 20px;
        margin: 0 5px;
    }
}

@media (max-width: 380px) {
    .navbar {
        max-width: 280px;
    }

    .nav-background {
        padding: 0 12px;
    }

    .nav-item {
        font-size: 0.8rem;
        padding: 4px 8px;
    }
}