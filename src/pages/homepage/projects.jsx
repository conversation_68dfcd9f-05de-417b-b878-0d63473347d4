import {motion} from 'motion/react';
import INFO from '../../data/user.js';
import React, { useState, useEffect } from 'react';
import Carousel from 'react-material-ui-carousel';
import Project from '../../components/projects/project.jsx';
import '../styles/carousel.css';

function Projects() {
	const [currentIndex, setCurrentIndex] = useState(0);

	// Handle carousel changes and update active dots
	const handleCarouselChange = (index) => {
		setCurrentIndex(index);
	};

	// Update active dots based on current index
	useEffect(() => {
		const updateActiveDots = () => {
			// Find only the indicator buttons (not navigation arrows)
			const indicators = document.querySelectorAll('.carousel-indicators button:not(.carousel-nav-btn)');

			indicators.forEach((indicator, index) => {
				// Remove active class from all indicators
				indicator.classList.remove('carousel-active');

				// Add active class to current indicator
				if (index === currentIndex) {
					indicator.classList.add('carousel-active');
				}
			});
		};

		// Update dots after carousel renders
		const timeoutId = setTimeout(updateActiveDots, 150);
		return () => clearTimeout(timeoutId);
	}, [currentIndex]);

	return (
		<React.Fragment>
			<section id="projects" className="scroll-child">
				<div className="white-space"></div>
				<motion.div initial={{opacity: 0}}
				            whileInView={{opacity: 1}}
				            transition={{duration: 0.75}}
				            className="homepage-container bg-blur">
					<div className="title projects-title">
						<h2>Skills</h2>
					</div>

					<div className="projects-list" role="region" aria-label="Skills carousel">
						<Carousel
							navButtonsAlwaysVisible={true}
							navButtonsProps={{
								className: "carousel-nav-btn"
							}}
							navButtonsWrapperProps={{
								className: "nav-buttons-wrapper"
							}}
							indicatorContainerProps={{
								className: "carousel-indicators"
							}}
							indicatorIconButtonProps={{
								className: "carousel-indicator-btn"
							}}
							className="skills-carousel"
							animation="slide"
							autoPlay={false}
							interval={6000}
							fullHeightHover={false}
							indicators={true}
							cycleNavigation={true}
							swipe={true}
							onChange={handleCarouselChange}
							index={currentIndex}
							style={{ width: '100%' }}
						>
							{INFO.projects.map((project, index) => (
								<div className="carousel-slide" key={index}>
									<Project
										logo={project.logo}
										title={project.title}
										description={project.description}
									/>
								</div>
							))}
						</Carousel>
					</div>
				</motion.div>
			</section>
		</React.Fragment>
	);
}

export default Projects;
