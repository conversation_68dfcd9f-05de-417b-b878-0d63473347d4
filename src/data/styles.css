:root {
    /* ------- iOS-Inspired Base Colors for WCAG Compliance ------- */
    --primary-color: #1c1c1e; /* iOS primary label color - darker for better contrast */
    --secondary-color: #3c3c43; /* iOS secondary label color */
    --tertiary-color: #8e8e93; /* iOS tertiary label color */
    --quaternary-color: #c7c7cc; /* iOS quaternary label color */
    --link-color: #6d6d70; /* iOS subtle text color */
    --accent-color: #f2f2f7; /* iOS system background color */

    /* ------- iOS-Inspired Glassmorphism Colors for Professional Appearance ------- */
    --glass-base: rgba(255, 255, 255, 0.32); /* Pure white base with iOS-like transparency */
    --glass-hover: rgba(249, 249, 249, 0.42); /* Warm off-white hover state */
    --glass-active: rgba(242, 242, 247, 0.52); /* iOS system gray active state */
    --glass-border: rgba(229, 229, 234, 0.45); /* iOS light gray border */
    --glass-border-hover: rgba(209, 209, 214, 0.55); /* iOS medium gray hover border */

    /* ------- iOS-Inspired Progress Bar Colors ------- */
    --progress-primary: #f2f2f7; /* iOS system background */
    --progress-secondary: #e5e5ea; /* iOS secondary system background */
    --progress-tertiary: #c7c7cc; /* iOS quaternary label */
    --progress-quaternary: #8e8e93; /* iOS tertiary label */
    --progress-glow: rgba(242, 242, 247, 0.7);
    --progress-glow-secondary: rgba(229, 229, 234, 0.5);

    /* ------- iOS-Inspired Interactive States ------- */
    --hover-overlay: rgba(242, 242, 247, 0.18);
    --active-overlay: rgba(229, 229, 234, 0.28);
    --focus-ring: rgba(209, 209, 214, 0.6);

    /* ------- iOS-Inspired Icon Colors for Better Accessibility ------- */
    --icon-default: #8e8e93; /* iOS secondary label color */
    --icon-hover: #3c3c43; /* iOS primary label color */
    --icon-active: #6d6d70; /* iOS tertiary label color */

    /* ------- iOS-Inspired Additional Accent Colors ------- */
    --success-color: #e5e5ea; /* iOS secondary system background */
    --warning-color: #c7c7cc; /* iOS quaternary label */
    --error-color: #8e8e93; /* iOS tertiary label */
    --info-color: #f2f2f7; /* iOS system background */

    /* ------- iOS Dark Mode Colors for Native Appearance ------- */
    --dark-glass-base: rgba(28, 28, 30, 0.78); /* iOS dark system background */
    --dark-glass-hover: rgba(44, 44, 46, 0.85); /* iOS dark secondary background */
    --dark-glass-active: rgba(58, 58, 60, 0.92); /* iOS dark tertiary background */
    --dark-glass-border: rgba(84, 84, 88, 0.65); /* iOS dark separator */
    --dark-glass-border-hover: rgba(99, 99, 102, 0.75); /* iOS dark separator hover */
    --dark-hover-overlay: rgba(255, 255, 255, 0.08); /* iOS dark hover overlay */
    --dark-active-overlay: rgba(255, 255, 255, 0.16); /* iOS dark active overlay */

    /* ------- iOS Dark Mode Icon Colors for Better Accessibility ------- */
    --dark-icon-default: #a1a1aa; /* iOS tertiary label */
    --dark-icon-hover: #f4f4f5; /* iOS primary label (light) */
    --dark-icon-active: #71717a; /* iOS quaternary label */

    /* ------- iOS Dark Mode Text Colors for WCAG Compliance ------- */
    --dark-primary-text: #ffffff; /* iOS primary label (dark mode) */
    --dark-secondary-text: #ebebf5; /* iOS secondary label (dark mode) */
    --dark-tertiary-text: #ebebf599; /* iOS tertiary label (dark mode) */
    --dark-muted-text: #ebebf54d; /* iOS quaternary label (dark mode) */
    /* ----------------------------------- */

    /* ------- fonts ------- */
    --primary-font: "Poppins", sans-serif;
    --secondary-font: "Montserrat", sans-serif;
    --heading-weight: 600;
    --body-weight: 400;
    --heading-line-height: 1.2;
    --body-line-height: 1.6;
    /* --------------------- */

    /* ------- spacing ------- */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    /* ----------------------- */

    /* ------- transitions ------- */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    /* --------------------------- */
}
